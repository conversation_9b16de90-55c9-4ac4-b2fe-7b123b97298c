# MUSNGR 🎵

Turn your audio content into stunning videos effortlessly with MUSNGR. Simply upload your tracks, select visuals, and let our platform handle the video creation—no editing skills needed!

## 🌟 Features

- **Audio to Video Conversion**: Transform audio files into engaging videos
- **Custom Backgrounds**: Upload images or generate AI-powered backgrounds
- **YouTube Integration**: Direct upload to YouTube with full metadata control
- **Real-time Analytics**: Track your video performance
- **Multiple Export Options**: Various video formats and quality settings
- **User-friendly Interface**: Intuitive drag-and-drop interface

## 🏗️ Project Structure

```
MUSNGR/
├── frontend-main/          # Next.js frontend application
│   ├── src/
│   │   ├── app/            # Next.js app router pages
│   │   ├── components/     # React components
│   │   ├── lib/            # Utility libraries
│   │   └── services/       # API services
│   ├── public/             # Static assets
│   └── package.json
├── backend-main/           # Backend services (Node.js + Rust)
│   ├── src/                # Rust backend source
│   ├── index.js            # Node.js backend
│   └── package.json
├── netlify.toml            # Netlify deployment configuration
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Google Cloud Console account (for YouTube API)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/MUSNGR.git
   cd MUSNGR
   ```

2. **Install frontend dependencies**
   ```bash
   cd frontend-main
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your environment variables:
   - `GOOGLE_CLIENT_ID` - Google OAuth client ID
   - `GOOGLE_CLIENT_SECRET` - Google OAuth client secret
   - `NEXTAUTH_SECRET` - NextAuth secret key
   - `NEXT_PUBLIC_YOUTUBE_API_KEY` - YouTube Data API key

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🌐 Deployment

### Netlify Deployment

The project is configured for easy Netlify deployment:

1. **Connect your GitHub repository** to Netlify
2. **Set environment variables** in Netlify dashboard
3. **Deploy** - Netlify will automatically build and deploy

Build settings:
- Build command: `npm run build`
- Publish directory: `frontend-main/.next`
- Base directory: `frontend-main`

### Environment Variables for Production

Set these in your Netlify dashboard:

```
NEXTAUTH_URL=https://your-site.netlify.app
NEXTAUTH_SECRET=your-secret-key
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
NEXT_PUBLIC_YOUTUBE_API_KEY=your-youtube-api-key
```

## 🔧 Configuration

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create OAuth 2.0 credentials
5. Add authorized origins and redirect URIs

### YouTube API Setup

1. Enable YouTube Data API v3 in Google Cloud Console
2. Create an API key
3. Add the API key to your environment variables

## 📱 Usage

1. **Sign in** with your Google account
2. **Upload audio file** (MP3, WAV, etc.)
3. **Add image** or generate background
4. **Configure video settings** (title, description, privacy)
5. **Create and upload** to YouTube

## 🛠️ Development

### Frontend Development

```bash
cd frontend-main
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
```

### Backend Development (Optional)

The current implementation uses Next.js API routes, but alternative backends are available:

```bash
cd backend-main
npm install          # Node.js backend
cargo run           # Rust backend
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

For support, email <EMAIL> or join our Discord community.

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- YouTube API for video hosting
- All contributors and users

---

Made with ❤️ by the MUSNGR team
