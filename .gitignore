# MUSNGR Project - Root .gitignore

# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js (Frontend)
frontend-main/.next/
frontend-main/out/
frontend-main/build/
frontend-main/.vercel/
frontend-main/*.tsbuildinfo
frontend-main/next-env.d.ts

# Rust (Backend)
backend-main/target/
backend-main/Cargo.lock
backend-main/config.toml

# Uploads and temporary files
backend-main/uploads/
backend-main/temp/
*/uploads/
*/temp/

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
build/
dist/
out/

# Package files
*.tgz
*.tar.gz

# Local development
.local

# Netlify
.netlify/
